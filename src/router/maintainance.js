import Index from "@/layout/Index.vue";

export default [
  {
    path: "/maintainance",
    name: "maintainance",
    component: Index,
    redirect: "/maintainance/faultWorkOrder",
    meta: {
      auth: true, // 是否需要登录
      keepAlive: true, // 是否缓存组件
      title: "运维管理",
    },
    children: [
      {
        path: "solution",
        name: "maintainanceSolution",
        redirect: "/maintainance/solution/index",
        meta: {
          auth: true,
          keepAlive: true,
          title: "方案库",
          parent: "运维管理",
        },
        children: [
          {
            path: "index",
            name: "solutionIndex",
            component: () => import("@/views/maintainance/solution/index.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "方案列表",
              parent: "方案库",
            },
          }
        ]
      },
      {
        path: "faultCategory",
        name: "maintainanceFaultCategory",
        redirect: "/maintainance/faultCategory/list",
        meta: {
          auth: true,
          keepAlive: true,
          title: "故障库",
          parent: "运维管理",
        },
        children: [
          {
            path: "list",
            name: "FaultCategoryList",
            component: () => import("@/views/maintainance/faultCategory/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "故障库",
              parent: "故障库",
            },
          }
        ]
      },
      {
        path: "station",
        name: "maintainanceStation",
        redirect: "/maintainance/station/list",
        meta: {
          auth: true,
          keepAlive: true,
          title: "电站中心",
          parent: "运维管理",
        },
        children: [
          {
            path: "list",
            name: "MaintainanceStationList",
            component: () => import("@/views/maintainance/station/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "电站列表",
              parent: "电站中心",
            },
          },
          {
            path: "detail",
            name: "MaintainanceStationDetail",
            component: () => import("@/views/maintainance/station/detail.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "电站详情",
              parent: "电站中心",
            },
          }
        ]
      },
      {
        path: "config",
        name: "maintainanceConfig",
        redirect: "/maintainance/config/inspection/list",
        meta: {
          auth: true,
          keepAlive: true,
          title: "基础资源配置",
          parent: "运维管理",
        },
        children: [
          {
            path: "inspection",
            name: "configInspectionList",
            component: () => import("@/views/maintainance/config/inspection/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "巡检基础配置",
              parent: "基础资源配置",
            },
          },
          {
            path: "workOrder",
            name: "configWorkOrder",
            component: () => import("@/views/maintainance/config/workOrder/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "工单基础配置",
              parent: "基础资源配置",
            },
          },
          {
            path: "report",
            name: "configReport",
            component: () => import("@/views/maintainance/config/report/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "报表基础配置",
              parent: "基础资源配置",
            },
          }
        ]
      },
      {
        path: "faultWorkOrder",
        name: "maintainanceFaultWorkOrder",
        redirect: "/maintainance/faultWorkOrder/list",
        meta: {
          auth: true,
          keepAlive: true,
          title: "工单大厅",
          parent: "运维管理",
        },
        children: [
          {
            path: "list/index",
            name: "faultWorkOrderListIndex",
            component: () => import("@/views/maintainance/faultWorkOrder/list/index.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "运维工单",
              parent: "工单大厅",
            },
          },
          {
            path: "list/audit",
            name: "faultWorkOrderListAudit",
            component: () => import("@/views/maintainance/faultWorkOrder/list/audit.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "审核工单",
              parent: "工单大厅",
            },
          },
          {
            path: "list/my",
            name: "faultWorkOrderListMy",
            component: () => import("@/views/maintainance/faultWorkOrder/list/my.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "提报工单",
              parent: "工单大厅",
            },
          },
          {
            path: "detail",
            name: "faultWorkOrderDetail",
            component: () => import("@/views/maintainance/faultWorkOrder/detail.vue"),
            meta: {
              auth: false,
              keepAlive: false,
              title: "工单详情",
              parent: "工单大厅",
            },
          },
          {
            path: "efficient",
            name: "faultWorkOrderEfficient",
            component: () => import("@/views/maintainance/faultWorkOrder/efficient.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "工单效率",
              parent: "工单大厅",
            },
          },
        ]
      },
      {
        path: "inspect",
        name: "maintainanceInspect",
        redirect: "/maintainance/inspect/plan/list",
        meta: {
          auth: true,
          keepAlive: true,
          title: "巡检管理",
          parent: "运维管理",
        },
        children: [
          {
            path: "plan/list",
            name: "InspectPlanList",
            component: () => import("@/views/maintainance/inspect/plan/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "巡检计划",
              parent: "巡检管理",
            },
          },
          {
            path: "task/list",
            name: "InspectTaskList",
            component: () => import("@/views/maintainance/inspect/task/list.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "巡检任务",
              parent: "巡检管理",
            },
          },
          {
            path: "task/detail",
            name: "InspectTaskDetail",
            component: () => import("@/views/maintainance/inspect/task/detail.vue"),
            meta: {
              auth: false,
              keepAlive: false,
              title: "巡检任务详情",
              parent: "巡检管理",
            },
          },
        ]
      },
      {
        path: "elecBill",
        name: "maintainanceElecBill",
        redirect: "/maintainance/elecBill/index",
        meta: {
          auth: true,
          keepAlive: true,
          title: "电费管理",
          parent: "运维管理",
        },
        children: [
          {
            path: "index",
            name: "elecBillIndex",
            component: () => import("@/views/maintainance/elecBill/index.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "电费管理",
              parent: "电费管理",
            },
          },
          {
            path: "edit",
            name: "elecBillEdit",
            component: () => import("@/views/maintainance/elecBill/edit.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "电费编辑",
              parent: "电费管理",
            },
          },
          {
            path: "detail",
            name: "elecBillDetail",
            component: () => import("@/views/maintainance/elecBill/detail.vue"),
            meta: {
              auth: false,
              keepAlive: false,
              title: "电费详情",
              parent: "电费管理",
            },
          },
        ]
      },
      {
        path: "report",
        name: "maintainanceReport",
        redirect: "/maintainance/report/index",
        meta: {
          auth: true,
          keepAlive: true,
          title: "报表大厅",
          parent: "运维管理",
        },
        children: [
          {
            path: "index",
            name: "ReportIndex",
            component: () => import("@/views/maintainance/report/index.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "报表管理",
              parent: "报表大厅",
            },
          },
        ]
      },
      {
        path: "message",
        name: "maintainanceMessage",
        redirect: "/maintainance/message/index",
        meta: {
          auth: true,
          keepAlive: true,
          title: "消息管理",
          parent: "运维管理",
        },
        children: [
          {
            path: "index",
            name: "MessageIndex",
            component: () => import("@/views/maintainance/message/index.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "报表管理",
              parent: "报表大厅",
            },
          },
          {
            path: "send",
            name: "MessageSend",
            component: () => import("@/views/maintainance/message/send.vue"),
            meta: {
              auth: true,
              keepAlive: true,
              title: "发送消息",
              parent: "报表大厅",
            },
          },
        ]
      },
    ]
  },
];