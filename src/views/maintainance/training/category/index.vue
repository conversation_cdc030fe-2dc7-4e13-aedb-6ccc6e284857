<template>
  <div class="wrap">
    <div class="cus-container">
      <!-- 搜索表单 -->
      <div class="cus-header">
        <el-form :model="searchForm" label-width="80px" :inline="true">
          <div class="search-form">
            <el-form-item label="分类名称">
              <el-input v-model="searchForm.name" placeholder="请输入分类名称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="创建人">
              <el-input v-model="searchForm.createdBy" placeholder="请输入创建人" clearable style="width: 200px" />
            </el-form-item>

            <!-- 查询按钮组 -->
            <div class="search-buttons">
              <el-button type="default" @click="onReset">重置</el-button>
              <el-button type="primary" @click="queryList">查询</el-button>
            </div>
          </div>
        </el-form>
      </div>

      <!-- 主内容区域 -->
      <div class="cus-main">
        <div class="cus-list" v-loading="loading">
          <!-- 操作按钮 -->
          <div style="text-align: right; margin-bottom: 16px;">
            <el-button type="success" plain @click="handleAdd">新增分类</el-button>
          </div>

          <!-- 数据表格 -->
          <el-table :data="listArr" class="cus-table">
            <el-table-column align="center" prop="name" label="分类名称" min-width="200">
              <template #default="scope">
                {{ scope.row.name || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdBy" label="创建人" width="150">
              <template #default="scope">
                {{ scope.row.createdBy || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
              <template #default="scope">
                {{ scope.row.createdAt ? formatDateTime(scope.row.createdAt) : '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updatedBy" label="更新人" width="150">
              <template #default="scope">
                {{ scope.row.updatedBy || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updatedAt" label="更新时间" width="180">
              <template #default="scope">
                {{ scope.row.updatedAt ? formatDateTime(scope.row.updatedAt) : '-' }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <el-pagination v-model:current-page="pagination.pageNum" v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total"
            @size-change="changeSize" @current-change="changeCurrent" />
        </div>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/Operate.vue';

// 搜索表单
const searchForm = reactive({
  name: '',
  createdBy: ''
});

// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getQuestionCategoryPage,
  () => searchForm,
  { manual: true }
);

// 操作弹窗相关
const operateVisible = ref(false);
const operateData = ref({});

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 重置搜索表单
const onReset = () => {
  searchForm.name = '';
  searchForm.createdBy = '';
  queryList();
};

// 新增分类
const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

// 编辑分类
const handleEdit = (row) => {
  operateData.value = { ...row };
  operateVisible.value = true;
};

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该分类吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteQuestionCategory({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '删除失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消删除
  });
};

// 操作成功回调
const handleOperateSuccess = () => {
  getList();
};

// 页面初始化
onMounted(() => {
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}
</style>